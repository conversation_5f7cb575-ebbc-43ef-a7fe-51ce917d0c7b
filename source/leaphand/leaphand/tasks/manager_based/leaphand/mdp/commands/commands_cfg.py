# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from dataclasses import MISSING

from isaaclab.managers import CommandTermCfg
from isaaclab.utils import configclass
from isaaclab.markers import VisualizationMarkersCfg
import isaaclab.sim as sim_utils
from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR

from .rotation_axis_command import RotationAxisCommand


@configclass
class RotationAxisVisualizerCfg:
    """旋转轴可视化配置类"""

    # 基础参数
    enabled: bool = True
    """是否启用旋转轴可视化"""

    # 位置参数
    offset_above_object: float = 0.15
    """箭头在物体上方的偏移距离（米）"""

    # 尺寸参数
    arrow_length: float = 0.12
    """箭头长度（米）"""

    arrow_thickness: float = 0.008
    """箭头粗细（米）"""

    # 颜色参数
    arrow_color: tuple[float, float, float] = (1.0, 0.2, 0.2)
    """箭头颜色 (R, G, B)，默认红色"""

    # 透明度
    opacity: float = 0.8
    """箭头透明度 [0.0-1.0]"""


@configclass
class RotationAxisCommandCfg(CommandTermCfg):
    """旋转轴命令项的配置类。

    更多详细信息请参考 :class:`RotationAxisCommand` 类。
    """

    class_type: type = RotationAxisCommand
    resampling_time_range: tuple[float, float] = (1e6, 1e6)  # 默认不基于时间重采样

    rotation_axis_mode: str = "z_axis" # 课程学习会动态调整
    """旋转轴模式。可选项: 'z_axis'(Z轴), 'x_axis'(X轴), 'y_axis'(Y轴), 'random'(随机), 'mixed'(混合)。"""

    change_rotation_axis_interval: int = 0
    """更换旋转轴的间隔步数。0表示不更换。"""

    rotation_axis_noise: float = 0.0
    """添加到旋转轴的噪声。范围: [0.0, 1.0]。"""

    debug_vis: bool = False
    """是否可视化旋转轴命令。"""

    visualizer_cfg: RotationAxisVisualizerCfg = RotationAxisVisualizerCfg()
    """旋转轴可视化配置。"""
