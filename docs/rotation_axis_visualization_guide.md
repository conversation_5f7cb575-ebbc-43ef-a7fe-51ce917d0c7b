# LeapHand连续旋转环境 - 旋转轴可视化指南

## 功能概述

旋转轴可视化功能在物体上方显示一个红色箭头，指示当前的旋转轴方向。

**重要说明**：
- **旋转轴定义**：旋转轴是在**世界坐标系**中定义的固定轴向量
- **物体旋转**：物体绕这个固定的世界坐标系轴旋转，而不是绕物体自身的局部坐标系轴
- **箭头含义**：箭头方向始终指向世界坐标系中的固定轴方向
- **箭头位置**：为便于观察，箭头位置跟随物体移动，但方向保持固定

箭头遵循右手螺旋定则：拇指指向箭头方向，其余手指弯曲方向为正旋转方向。

## 可视化特性

### 1. 箭头显示
- **颜色**：红色箭头，半透明显示
- **位置**：物体上方固定距离（默认0.15米）
- **方向**：指向旋转轴正方向
- **尺寸**：可配置的长度和粗细

### 2. 动态更新
- **位置跟随**：箭头位置跟随物体移动（便于观察）
- **方向固定**：箭头方向始终指向世界坐标系中的固定轴
- **轴切换更新**：课程学习改变旋转轴时，箭头方向更新到新的世界坐标系轴

### 3. 坐标系说明
- **世界坐标系**：固定不变的全局坐标系，原点通常在场景中心
- **旋转轴定义**：在世界坐标系中定义，例如：
  - Z轴旋转：(0, 0, 1) - 始终指向世界Z轴正方向
  - X轴旋转：(1, 0, 0) - 始终指向世界X轴正方向
  - Y轴旋转：(0, 1, 0) - 始终指向世界Y轴正方向
- **物体旋转**：物体绕这些固定的世界坐标系轴旋转

### 4. 右手螺旋定则
- **拇指方向**：指向箭头方向（旋转轴正方向）
- **手指弯曲**：其余四指弯曲方向为正旋转方向
- **物理意义**：符合物理学中的角动量和力矩方向约定

## 配置参数

### 基础配置
```python
# 在环境配置中启用可视化
commands.rotation_axis.debug_vis = True
```

### 高级配置
```python
# 自定义可视化参数
visualizer_cfg = RotationAxisVisualizerCfg(
    enabled=True,                    # 启用可视化
    offset_above_object=0.15,        # 箭头在物体上方的距离（米）
    arrow_length=0.12,               # 箭头长度（米）
    arrow_thickness=0.008,           # 箭头粗细（米）
    arrow_color=(1.0, 0.2, 0.2),    # 箭头颜色 (R, G, B)
    opacity=0.8,                     # 透明度 [0.0-1.0]
)
```

## 使用方法

### 1. 启用可视化
在环境配置文件中设置：
```python
rotation_axis = RotationAxisCommandCfg(
    # ... 其他配置 ...
    debug_vis=True,  # 启用旋转轴可视化
)
```

### 2. 运行环境
```bash
cd ~/isaac && source .venv/bin/activate
cd leaphand
python scripts/rl_games/train.py --task=Isaac-Leaphand-ContinuousRot-Manager-v0 --num_envs=4
```

### 3. 观察可视化
在Isaac Sim中观察：
- 红色箭头出现在物体上方
- 箭头方向指示旋转轴
- 随着课程学习进展，箭头方向可能会改变

## 测试验证

### 运行测试脚本
```bash
cd leaphand
python scripts/test_rotation_axis_visualization.py --num_envs=4
```

### 测试内容
1. **Z轴旋转**：箭头垂直向上
2. **X轴旋转**：箭头水平指向X轴正方向
3. **Y轴旋转**：箭头水平指向Y轴正方向
4. **随机旋转**：箭头指向随机方向

## 参数调优建议

### 位置调整
- `offset_above_object`：根据物体大小调整
  - 小物体：0.10-0.15米
  - 大物体：0.20-0.30米

### 尺寸调整
- `arrow_length`：根据场景比例调整
  - 近距离观察：0.08-0.12米
  - 远距离观察：0.15-0.25米
- `arrow_thickness`：保持与长度的合理比例
  - 推荐比例：thickness = length * 0.05-0.1

### 外观调整
- `arrow_color`：
  - 红色：(1.0, 0.2, 0.2) - 默认，醒目
  - 蓝色：(0.2, 0.2, 1.0) - 冷色调
  - 绿色：(0.2, 1.0, 0.2) - 自然色
- `opacity`：
  - 0.6-0.8：半透明，不遮挡视线
  - 0.9-1.0：不透明，更醒目

## 故障排除

### 箭头不显示
1. 检查 `debug_vis=True` 是否设置
2. 确认 `visualizer_cfg.enabled=True`
3. 检查Isaac Sim是否正常运行

### 箭头位置不正确
1. 调整 `offset_above_object` 参数
2. 检查物体位置是否正确获取
3. 确认场景中物体名称为"object"

### 箭头方向错误
1. 验证旋转轴命令是否正确生成
2. 检查右手螺旋定则的理解
3. 对比不同旋转轴模式的显示

### 性能问题
1. 减少环境数量进行测试
2. 降低箭头复杂度（减小尺寸）
3. 在不需要时禁用可视化

## 技术实现

### 核心组件
- **VisualizationMarkers**：Isaac Lab的标准可视化框架
- **arrow_x.usd**：Isaac Nucleus提供的箭头模型
- **debug_vis机制**：标准的调试可视化接口

### 更新机制
- **位置更新**：每帧根据物体位置更新箭头位置
- **方向更新**：旋转轴改变时重新计算箭头方向
- **四元数计算**：使用数学工具计算从默认方向到目标方向的旋转

### 性能优化
- **实例化渲染**：使用UsdGeom.PointInstancer高效渲染多个箭头
- **条件更新**：仅在需要时更新可视化
- **异常处理**：优雅处理获取物体位置失败的情况
